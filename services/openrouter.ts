import { API_CONFIG } from '@/config/api';
import { Plant, CareInstructions } from '@/types/plant';

export interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

export interface PlantIdentificationData {
  scientificName: string;
  commonName: string;
  description: string;
  careInstructions: CareInstructions;
  tags: string[];
  confidence: number;
  diagnosis?: string;
  treatment?: string;
}

export class OpenRouterService {
  private static async makeRequest(imageUri: string, prompt: string): Promise<string> {
    try {
      const response = await fetch(`${API_CONFIG.OPENROUTER_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.OPENROUTER_API_KEY}`,
        },
        body: JSON.stringify({
          model: API_CONFIG.MODEL,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUri,
                  },
                },
              ],
            },
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
      }

      const data: OpenRouterResponse = await response.json();
      return data.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenRouter API error:', error);
      throw error;
    }
  }

  static async identifyPlant(imageUri: string): Promise<PlantIdentificationData> {
    const prompt = `Analyze this plant image and provide detailed information in the following JSON format:

{
  "scientificName": "Scientific name of the plant",
  "commonName": "Common name of the plant",
  "description": "Detailed description of the plant (2-3 sentences)",
  "careInstructions": {
    "light": "low|medium|high",
    "water": "low|medium|high", 
    "temperature": {"min": 15, "max": 30, "unit": "C"},
    "humidity": "low|medium|high",
    "soil": "Soil requirements description",
    "fertilizer": "Fertilizer requirements description",
    "toxicity": "none|mild|moderate|severe"
  },
  "tags": ["array", "of", "relevant", "tags"],
  "confidence": 0.95
}

IMPORTANT: Return ONLY the JSON object above, no additional text, explanations, or formatting. Start your response with { and end with }.`;

    const response = await this.makeRequest(imageUri, prompt);
    
    try {
      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      
      const parsed = JSON.parse(jsonString);
      
      // Validate required fields
      if (!parsed.scientificName || !parsed.commonName || !parsed.careInstructions) {
        throw new Error('Missing required fields in response');
      }
      
      return parsed;
    } catch (error) {
      console.error('Failed to parse OpenRouter response:', error);
      console.error('Raw response:', response);
      
      // Return a fallback response
      return {
        scientificName: 'Unknown species',
        commonName: 'Unknown plant',
        description: 'Unable to identify this plant. Please try again with a clearer image.',
        careInstructions: {
          light: 'medium',
          water: 'medium',
          temperature: { min: 18, max: 25, unit: 'C' },
          humidity: 'medium',
          soil: 'Well-draining potting mix',
          fertilizer: 'Monthly during growing season',
          toxicity: 'none'
        },
        tags: ['unknown'],
        confidence: 0.1
      };
    }
  }

  static async diagnosePlant(imageUri: string, problemDescription?: string): Promise<PlantIdentificationData> {
    const problemText = problemDescription ? `The user reports: "${problemDescription}". ` : '';
    
    const prompt = `Analyze this plant image for identification and health diagnosis. ${problemText}Provide detailed information in the following JSON format:

{
  "scientificName": "Scientific name of the plant",
  "commonName": "Common name of the plant", 
  "description": "Detailed description of the plant (2-3 sentences)",
  "careInstructions": {
    "light": "low|medium|high",
    "water": "low|medium|high",
    "temperature": {"min": 15, "max": 30, "unit": "C"},
    "humidity": "low|medium|high", 
    "soil": "Soil requirements description",
    "fertilizer": "Fertilizer requirements description",
    "toxicity": "none|mild|moderate|severe"
  },
  "tags": ["array", "of", "relevant", "tags"],
  "confidence": 0.95,
  "diagnosis": "Health assessment and any issues identified",
  "treatment": "Recommended treatment and care adjustments"
}

IMPORTANT: Focus on identifying any health issues, diseases, pests, or care problems. Provide specific treatment recommendations. Return ONLY the JSON object above, no additional text, explanations, or formatting. Start your response with { and end with }.`;

    const response = await this.makeRequest(imageUri, prompt);
    
    try {
      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      
      const parsed = JSON.parse(jsonString);
      
      // Validate required fields
      if (!parsed.scientificName || !parsed.commonName || !parsed.careInstructions) {
        throw new Error('Missing required fields in response');
      }
      
      return parsed;
    } catch (error) {
      console.error('Failed to parse OpenRouter response:', error);
      console.error('Raw response:', response);
      
      // Return a fallback response
      return {
        scientificName: 'Unknown species',
        commonName: 'Unknown plant',
        description: 'Unable to diagnose this plant. Please try again with a clearer image.',
        careInstructions: {
          light: 'medium',
          water: 'medium',
          temperature: { min: 18, max: 25, unit: 'C' },
          humidity: 'medium',
          soil: 'Well-draining potting mix',
          fertilizer: 'Monthly during growing season',
          toxicity: 'none'
        },
        tags: ['unknown'],
        confidence: 0.1,
        diagnosis: 'Unable to assess plant health from this image.',
        treatment: 'Please provide a clearer image or consult a plant expert.'
      };
    }
  }
}
