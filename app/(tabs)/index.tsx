import React, { useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, ScrollView } from 'react-native';
import { Stack } from 'expo-router';
import { Camera, Info } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { BloomSnapCamera } from '@/components/camera/CameraView';
import { IdentificationResultView } from '@/components/identification/IdentificationResult';
import { useIdentification } from '@/hooks/useIdentificationStore';
import { useGarden } from '@/hooks/useGardenStore';
import { recentlyIdentified } from '@/mocks/plants';
import { Platform } from 'react-native';

export default function SnapScreen() {
  const [showCamera, setShowCamera] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const { identifyPlant, currentResult, clearCurrentResult, isIdentifying } = useIdentification();
  const { addPlant } = useGarden();

  const handleCapture = async (uri: string) => {
    setShowCamera(false);
    setCapturedImage(uri);
    setShowPreview(true);
  };

  const handleIdentify = async () => {
    if (capturedImage) {
      setShowPreview(false);
      await identifyPlant(capturedImage);
    }
  };

  const handleAddToGarden = () => {
    if (currentResult) {
      addPlant(currentResult.plant);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }
  };

  const handleNewScan = () => {
    clearCurrentResult();
    setCapturedImage(null);
    setShowCamera(true);
  };

  const startCamera = () => {
    clearCurrentResult();
    setCapturedImage(null);
    setShowCamera(true);
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const handleRetakePhoto = () => {
    setCapturedImage(null);
    setShowPreview(false);
    setShowCamera(true);
  };

  if (showCamera) {
    return <BloomSnapCamera onCapture={handleCapture} onCancel={() => setShowCamera(false)} mode="identify" />;
  }

  if (showPreview && capturedImage) {
    return (
      <View style={styles.container} testID="preview-screen">
        <Stack.Screen options={{ title: 'Plant Identification Preview' }} />
        
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.previewContainer}>
            <Image source={{ uri: capturedImage }} style={styles.previewImage} />
            
            <View style={styles.previewContent}>
              <Text style={styles.previewTitle}>Ready to Identify</Text>
              <Text style={styles.previewSubtitle}>
                Our AI will analyze this image to identify your plant
              </Text>
              
              <View style={styles.previewActions}>
                <Button
                  title="Retake Photo"
                  variant="outline"
                  onPress={handleRetakePhoto}
                  style={styles.retakeButton}
                  testID="retake-button"
                />
                <Button
                  title="Identify Plant"
                  onPress={handleIdentify}
                  style={styles.identifyButton}
                  loading={isIdentifying}
                  testID="identify-button"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  if (currentResult) {
    return (
      <IdentificationResultView
        result={currentResult}
        onAddToGarden={handleAddToGarden}
        onNewScan={handleNewScan}
      />
    );
  }

  return (
    <View style={styles.container} testID="snap-screen">
      <Stack.Screen options={{ title: 'Plantst' }} />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Image
            source={{ uri: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2342&q=80' }}
            style={styles.headerImage}
          />
          <View style={styles.headerContent}>
            <Text style={styles.title}>Identify any plant in seconds</Text>
            <Text style={styles.subtitle}>
              Take a photo of any plant and get instant identification with care instructions
            </Text>
          </View>
        </View>

        <View style={styles.actionsContainer}>
          <Button
            title="Snap to Identify"
            onPress={startCamera}
            style={styles.snapButton}
            textStyle={styles.snapButtonText}
            testID="start-camera-button"
          />
          
          <View style={styles.featureCards}>
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.secondary }]}>
                <Camera size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Instant ID</Text>
              <Text style={styles.featureText}>Identify any plant with just a photo</Text>
            </Card>
            
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.tertiary }]}>
                <Camera size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Quick Snap</Text>
              <Text style={styles.featureText}>Fast and accurate identification</Text>
            </Card>
            
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.accent1 }]}>
                <Info size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Care Guide</Text>
              <Text style={styles.featureText}>Get detailed care instructions</Text>
            </Card>
          </View>
        </View>

        <View style={styles.recentContainer}>
          <Text style={styles.sectionTitle}>Recently Identified</Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.recentScroll}>
            {recentlyIdentified.map((plant) => (
              <TouchableOpacity key={plant.id} style={styles.recentItem} testID={`recent-plant-${plant.id}`}>
                <Image source={{ uri: plant.imageUrl }} style={styles.recentImage} />
                <Text style={styles.recentName}>{plant.commonName}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    position: 'relative',
    height: 200,
    marginBottom: 20,
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.background,
    marginBottom: 8,
  },

  subtitle: {
    fontSize: 16,
    color: Colors.background,
    opacity: 0.9,
  },
  actionsContainer: {
    padding: 20,
  },
  snapButton: {
    height: 60,
    borderRadius: 30,
    marginBottom: 24,
  },
  snapButtonText: {
    fontSize: 18,
  },
  featureCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  featureCard: {
    flex: 1,
    marginHorizontal: 4,
    padding: 12,
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  featureText: {
    fontSize: 12,
    color: Colors.textLight,
    textAlign: 'center',
  },
  recentContainer: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  recentScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  recentItem: {
    width: 120,
    marginRight: 16,
  },
  recentImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
    marginBottom: 8,
  },
  recentName: {
    fontSize: 14,
    color: Colors.text,
    textAlign: 'center',
  },
  previewContainer: {
    flex: 1,
  },
  previewImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  previewContent: {
    padding: 20,
  },
  previewTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
  },
  previewSubtitle: {
    fontSize: 16,
    color: Colors.textMuted,
    marginBottom: 24,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 12,
  },
  retakeButton: {
    flex: 1,
  },
  identifyButton: {
    flex: 2,
  },
});