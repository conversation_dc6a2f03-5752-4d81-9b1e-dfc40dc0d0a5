import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import { Stack } from 'expo-router';
import { Colors } from '@/constants/colors';
import { SearchBar } from '@/components/ui/SearchBar';
import { CategoryFilter } from '@/components/ui/CategoryFilter';
import { PlantGrid } from '@/components/ui/PlantGrid';
import { useFavorites } from '@/hooks/useFavoritesStore';
import { popularPlants } from '@/mocks/plants';
import { Plant } from '@/types/plant';

const categories = ['All', 'Indoor', 'Outdoor', 'Succulents', 'Tropical', 'Flowering'];

export default function DiscoverScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const { favorites, toggleFavorite } = useFavorites();

  const filteredPlants = popularPlants.filter((plant) => {
    const matchesSearch =
      plant.commonName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plant.scientificName.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategory === 'All' ||
      plant.tags.some((tag) => tag.toLowerCase() === selectedCategory.toLowerCase());

    return matchesSearch && matchesCategory;
  });

  const handlePlantPress = (plant: Plant) => {
    console.log('View plant details', plant.id);
  };

  return (
    <View style={styles.container} testID="discover-screen">
      <Stack.Screen options={{ title: 'Discover Plants' }} />

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search plants..."
      />

      <CategoryFilter
        categories={categories}
        selectedCategory={selectedCategory}
        onSelectCategory={setSelectedCategory}
      />

      {filteredPlants.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No plants found</Text>
          <Text style={styles.emptyText}>
            Try adjusting your search or category filter
          </Text>
        </View>
      ) : (
        <PlantGrid
          plants={filteredPlants}
          onPlantPress={handlePlantPress}
          onFavoritePress={toggleFavorite}
          favorites={favorites}
          numColumns={2}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
  },
});