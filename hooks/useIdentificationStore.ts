import { useState } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { Plant, IdentificationResult } from '@/types/plant';
import { popularPlants } from '@/mocks/plants';

export const [IdentificationProvider, useIdentification] = createContextHook(() => {
  const [results, setResults] = useState<IdentificationResult[]>([]);
  const [currentResult, setCurrentResult] = useState<IdentificationResult | null>(null);
  const [isIdentifying, setIsIdentifying] = useState(false);

  // Simulate plant identification with a mock implementation
  const identifyPlant = async (imageUri: string, problemDescription?: string): Promise<IdentificationResult> => {
    setIsIdentifying(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Randomly select a plant from the mock data
      const randomIndex = Math.floor(Math.random() * popularPlants.length);
      const identifiedPlant = popularPlants[randomIndex];
      
      // Generate a random confidence score between 0.85 and 0.99
      const confidence = 0.85 + Math.random() * 0.14;
      
      const result: IdentificationResult = {
        plant: identifiedPlant,
        confidence,
        timestamp: new Date(),
        imageUri,
        problemDescription,
      };
      
      setResults(prev => [result, ...prev]);
      setCurrentResult(result);
      
      return result;
    } finally {
      setIsIdentifying(false);
    }
  };

  const clearCurrentResult = () => {
    setCurrentResult(null);
  };

  return {
    results,
    currentResult,
    isIdentifying,
    identifyPlant,
    clearCurrentResult,
  };
});