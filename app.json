{"expo": {"name": "BloomSnap-Sonnet4", "slug": "bloomsnap", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.bloomsnap", "infoPlist": {"NSCameraUsageDescription": "Allow $(PRODUCT_NAME) to access your camera", "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone", "NSPhotoLibraryUsageDescription": "Allow $(PRODUCT_NAME) to access your photos"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.bloomsnap", "permissions": ["android.permission.VIBRATE", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.com/"}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}]], "experiments": {"typedRoutes": true}}}